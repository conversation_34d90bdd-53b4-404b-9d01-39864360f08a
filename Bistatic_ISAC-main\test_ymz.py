import numpy as np
import re
from pathlib import Path

def is_complex_matrix(file_path):
    """
    检查文件是否为复数矩阵
    
    参数:
    file_path: 文件路径
    
    返回:
    is_complex: 是否为复数矩阵
    matrix_shape: 矩阵形状(如果是矩阵)
    sample_data: 样本数据(前几行)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except Exception as e:
        return False, None, None, f"读取文件时出错: {str(e)}"
    
    # 预处理：移除空行和注释行
    processed_lines = []
    for line in lines:
        line = line.strip()
        if line and not line.startswith('#'):  # 跳过空行和注释
            processed_lines.append(line)
    
    if not processed_lines:
        return False, None, None, "文件为空或只包含注释/空行"
    
    # 检查每行是否包含复数
    complex_matrix = []
    complex_pattern = r'\(?[-+]?\d*\.?\d*[eE]?[-+]?\d*[+-]\d*\.?\d*[eE]?[-+]?\d*j\)?|[-+]?\d*\.?\d*[eE]?[-+]?\d*j'
    
    for i, line in enumerate(processed_lines):
        # 尝试分割行中的元素
        elements = re.findall(complex_pattern, line)
        if not elements:
            return False, None, None, f"第{i+1}行不包含有效的复数"
        
        # 转换为复数
        try:
            complex_row = [complex(element.replace(' ', '').replace('(', '').replace(')', '')) for element in elements]
            complex_matrix.append(complex_row)
        except ValueError:
            return False, None, None, f"第{i+1}行包含无效的复数格式"
    
    # 检查矩阵形状是否一致
    row_lengths = [len(row) for row in complex_matrix]
    if len(set(row_lengths)) > 1:
        return False, None, None, f"行长度不一致: {row_lengths}"
    
    # 提取样本数据
    sample_size = min(3, len(complex_matrix))
    sample_data = complex_matrix[:sample_size]
    
    return True, (len(complex_matrix), len(complex_matrix[0])), sample_data, "文件包含有效的复数矩阵"

# 文件路径
file_path = r"D:\biye\code\env2024-main 2\env2024-main\umi_v3\H_freq.txt"

# 检查文件是否存在
if not Path(file_path).exists():
    print(f"错误: 文件 '{file_path}' 不存在")
else:
    # 进行分析
    is_complex, shape, sample, message = is_complex_matrix(file_path)
    
    # 打印结果
    print("=" * 50)
    print("复数矩阵检查结果")
    print("=" * 50)
    print(f"文件路径: {file_path}")
    print(f"是否为复数矩阵: {'是' if is_complex else '否'}")
    
    if is_complex:
        print(f"矩阵形状: {shape[0]}行 x {shape[1]}列")
        print("\n样本数据 (前3行):")
        for i, row in enumerate(sample):
            print(f"第{i+1}行: {row}")
    else:
        print(f"错误信息: {message}")
    
    print("=" * 50)